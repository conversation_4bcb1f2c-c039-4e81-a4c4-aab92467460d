# Username Update Feature Implementation

## Overview

This implementation adds the ability to recreate user directories when a username changes and automatically add admin users to all engagements.

## Changes Made

### 1. Core Function Updates

**File: `api/internal/users/model.go`**

- **Modified `UpdateUserUsername`**: Now accepts additional parameters (`awsRootRegion`, `secretKey`) and calls SSH access management logic
- **Added `RecreateUserDirectoriesOnInstances`**: Main orchestration function that handles the directory recreation process
- **Added `recreateUserDirectoriesForEngagement`**: Handles directory recreation for a specific engagement
- **Added `recreateUserDirectoryOnInstanceWithCreds`**: Performs the actual SSH operations on individual instances

**File: `api/internal/users/sshkey.go`**

- **Added `RevokeUserSshAccessForUsernameOnInstances`**: Removes SSH access for a specific username across all instances
- **Modified SSH connection logic**: Removed AdminUser attempts, uses standard OS users (admin, ubuntu, ec2-user, azureuser)

**File: `api/internal/users/assignments.go`**

- **Modified `performKeyManagementToInstance`**: Now uses provider-specific admin users (admin for AWS, azureuser for Azure)
- **Enabled Azure instance processing**: Uncommented Azure tenant handling in user assignments

### 2. API Handler Updates

**File: `api/cmd/api/users.go`**

- Updated the `edit-user-username` endpoint handler to pass the required `awsRootRegion` and `secretKey` parameters to `UpdateUserUsername`

### 3. Import Updates

- Added imports for `strings` and `deployments/keys` packages to support SSH operations

## How It Works

### Username Update Flow

1. **Validation**: Check if new username is available
2. **Database Update**: Update the username in the database
3. **SSH Access Management** (Asynchronous): If user has SSH key and valid username:
   - **Revoke Phase**: Remove SSH access for old username across all instances
   - **Recreate Phase**: Create new user directories with new username
   - Get all engagements the user belongs to
   - For each engagement:
     - Get all cloud instances (AWS and Azure)
     - Connect via SSH using engagement's private SSH key
     - Remove old username's SSH keys (preserving home directory)
     - Create new user directory with new username and SSH keys
4. **Admin Sync**: If user is admin, add to all engagements (existing functionality)

### SSH Operations

The implementation uses the existing SSH infrastructure:
- **AWS Instances**: Connects as "admin" user using engagement's private SSH key
- **Azure Instances**: Connects as "azureuser" user using engagement's private SSH key
- Uses `AddUser` function to create new Unix user with home directory
- Sets up SSH keys and sudoers permissions
- **Revokes old username access**: Removes SSH keys from old username to prevent login
- **Creates new username access**: Sets up new user directory with SSH keys
- Preserves old directories to prevent data loss

### Error Handling

- **Non-blocking**: Username update succeeds even if SSH operations fail
- **Asynchronous Processing**: SSH operations run in background goroutines
- **Detailed logging**: Comprehensive logging with prefixes (USERNAME_UPDATE, REVOKE_ACCESS, RECREATE_INST) for troubleshooting
- **Continues processing**: Other instances/engagements continue if one fails
- **Provider-specific handling**: Correctly handles both AWS and Azure instances
- **Sequential operations**: Revoke and recreate run sequentially to avoid race conditions

## Security Considerations

1. **SSH Key Management**: Uses existing secure key storage (AWS Secrets Manager)
2. **Passphrase Encryption**: SSH key passphrases remain encrypted using existing encryption
3. **Access Control**: Only users can update their own usernames
4. **Data Preservation**: Old directories are preserved to prevent accidental data loss

## Testing

### Automated Testing

Use the provided test script:
```bash
USER_ID=your-user-id AUTH_TOKEN=your-token ./api/scripts/test_username_update.sh
```

### Manual Testing

1. **Basic Test**: Change username in UI, verify database update
2. **SSH Test**: Connect to instances with new username
3. **Admin Test**: Verify admin users are added to all engagements
4. **Error Test**: Test with invalid usernames, network issues

See `api/test_username_update.md` for detailed testing procedures.

## Verification Steps

### 1. Database Verification
```sql
-- Check username update
SELECT custom_username FROM users WHERE id = 'USER_ID';

-- Check admin engagement assignments (for admin users)
SELECT COUNT(*) FROM engagements_users WHERE user_id = 'ADMIN_USER_ID';
```

### 2. Instance Verification
```bash
# SSH to instance with new username (should work)
ssh new_username@INSTANCE_IP

# SSH to instance with old username (should fail)
ssh old_username@INSTANCE_IP  # This should be rejected

# Check directories exist
ls -la /home/
test -d /home/<USER>"New directory exists"
test -d /home/<USER>"Old directory preserved"

# Check SSH keys
cat /home/<USER>/.ssh/authorized_keys
# Old username should have no SSH keys
sudo cat /home/<USER>/.ssh/authorized_keys 2>/dev/null || echo "No SSH keys for old username (expected)"

# Check sudoers
sudo cat /etc/sudoers.d/fusionx | grep new_username
```

### 3. Log Verification
Look for these log messages:
- `USERNAME_UPDATE_REVOKE: COMPLETED for user=X oldUsername='Y'`
- `USERNAME_UPDATE_RECREATE: COMPLETED for user=X old='Y' new='Z'`
- `REVOKE_ACCESS: user=X instance node_id=Y ip=Z - SSH connection SUCCESS as user 'admin'` (AWS)
- `REVOKE_ACCESS: user=X instance node_id=Y ip=Z - SSH connection SUCCESS as user 'azureuser'` (Azure)
- `RECREATE_INST: engagement=X node_id=Y ip=Z - SSH connection SUCCESS as user 'admin'` (AWS)
- `RECREATE_INST: engagement=X node_id=Y ip=Z - SSH connection SUCCESS as user 'azureuser'` (Azure)

## Rollback Plan

If issues occur:
1. **Database**: Username can be changed back via API
2. **Instances**: Old directories are preserved, so users can still access via old username
3. **Manual Cleanup**: SSH to instances and manually manage user accounts if needed

## Performance Considerations

- **Asynchronous**: SSH operations don't block the API response
- **Parallel Processing**: Each instance is processed independently
- **Timeout Handling**: SSH connections have timeouts to prevent hanging
- **Resource Usage**: Minimal additional load on database and API

## Future Enhancements

1. **Old Directory Cleanup**: Option to remove old directories after confirmation
2. **Batch Operations**: Support for updating multiple users
3. **Audit Trail**: Enhanced logging for compliance
4. **Retry Logic**: Automatic retry for failed SSH operations
5. **Notification System**: Alert admins of failed directory creations

## Dependencies

- Existing SSH key infrastructure
- AWS Secrets Manager access
- Network connectivity to cloud instances
- Proper security group configurations for SSH access
