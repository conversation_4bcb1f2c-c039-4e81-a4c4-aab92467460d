package keys

import (
	"bytes"
	"fmt"
	"math/rand"
	"net"
	"strings"
	"time"

	"golang.org/x/crypto/ssh"
)

// SSHClient wraps SSH connection parameters and provides a method to connect with exponential backoff and jitter
type SSHClient struct {
	Address    string
	User       string
	PrivateKey string
	Passphrase string
	MaxRetries int
	BaseDelay  time.Duration
	client     *ssh.Client
}

// NewSSHClient initializes an SSHClient instance
func NewSSHClient(address, user, privateKey string, passphrase string, maxRetries int, baseDelay time.Duration) *SSHClient {

	return &SSHClient{
		Address:    address,
		User:       user,
		PrivateKey: privateKey,
		Passphrase: passphrase,
		MaxRetries: maxRetries,
		BaseDelay:  baseDelay,
	}
}

// Connect tries to establish an SSH connection with exponential backoff and jitter
func (s *SSHClient) Connect() error {
	var err error

	for i := 0; i < s.MaxRetries; i++ {
		fmt.Printf("SSH connect attempt %d/%d: user=%s addr=%s\n", i+1, s.<PERSON>, s.<PERSON>r, s.Address)
		s.client, err = s.sshDial()
		if err == nil {
			return nil
		}

		// Exponential backoff
		backoff := s.BaseDelay * (1 << i)

		// Jitter up to half of the backoff duration
		jitter := time.Duration(rand.Int63n(int64(backoff) / 2))
		sleepDuration := backoff + jitter

		fmt.Printf("SSH connection failed: %s. Retrying in %s...\n", err, sleepDuration)
		time.Sleep(sleepDuration)
	}
	return fmt.Errorf("failed to connect to SSH after %d retries: %v", s.MaxRetries, err)
}

// Close closes the SSH client connection
func (s *SSHClient) Close() error {
	if s.client != nil {
		return s.client.Close()
	}
	return nil
}

// sshDial dials the SSH server using the client's configuration
func (s *SSHClient) sshDial() (*ssh.Client, error) {
	// Parse the private key with passphrase
	signer, err := ssh.ParsePrivateKeyWithPassphrase([]byte(s.PrivateKey), []byte(s.Passphrase))
	if err != nil {
		return nil, fmt.Errorf("unable to parse private key: %v", err)
	}

	// Log the key fingerprint and who/where we're connecting as
	fp := ssh.FingerprintSHA256(signer.PublicKey())
	fmt.Printf("SSH handshake: user=%s addr=%s key_fp=%s\n", s.User, s.Address, fp)

	config := &ssh.ClientConfig{
		User: s.User,
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		},
		HostKeyCallback: func(hostname string, remote net.Addr, hostKey ssh.PublicKey) error {
			serverFP := ssh.FingerprintSHA256(hostKey)
			fmt.Printf("SSH server hostkey: host=%s remote=%s fp=%s\n", hostname, remote, serverFP)
			return nil
		},
	}

	client, err := ssh.Dial("tcp", s.Address, config)
	if err != nil {
		return nil, fmt.Errorf("failed to dial: %v", err)
	}
	fmt.Printf("SSH connected: user=%s addr=%s key_fp=%s\n", s.User, s.Address, fp)

	return client, nil
}

// AddUser creates a unix user to a remote server through SSH and adds their public SSH key
func (s *SSHClient) AddUser(username string, publicSshKey string, sudoersGroupName string) error {
	// Check if user exists
	_, err := s.runCommand(fmt.Sprintf("id %s", username))
	if err != nil { // User doesn't exist, create user directories from scratch
		// Add user
		_, err = s.runCommand(fmt.Sprintf("sudo /usr/sbin/useradd -s /bin/bash  %s --create-home", username))
		if err != nil {
			return fmt.Errorf("adding user %s failed: %s", username, err)
		}

		// in-group admin
		_, err = s.runCommand(fmt.Sprintf("echo '%s ALL=(ALL) NOPASSWD:ALL' | sudo tee -a /etc/sudoers.d/%s", username, sudoersGroupName))
		if err != nil {
			return fmt.Errorf("adding user %s to sudoers failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chmod 400 /etc/sudoers.d/%s", sudoersGroupName))
		if err != nil {
			return fmt.Errorf("changing sudoers group %s permissions to 400 failed: %s", sudoersGroupName, err)
		}

		// push auth key set (create .ssh, write authorized_keys, fix perms)
		_, err = s.runCommand(fmt.Sprintf("sudo mkdir -p /home/<USER>/.ssh", username))
		if err != nil {
			return fmt.Errorf("creating user %s SSH directory failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("echo -e '%s' | sudo tee /home/<USER>/.ssh/authorized_keys > /dev/null", publicSshKey, username))
		if err != nil {
			return fmt.Errorf("writing user's %s public SSH key failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chown -R %s:%s /home/<USER>/.ssh", username, username, username))
		if err != nil {
			return fmt.Errorf("changing ownership of SSH directory for user %s failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chmod 700 /home/<USER>/.ssh && sudo chmod 600 /home/<USER>/.ssh/authorized_keys", username, username))
		if err != nil {
			return fmt.Errorf("setting permissions on SSH dir and authorized_keys for user %s failed: %s", username, err)
		}
	} else { // User exists, ensure .ssh and authorized_keys are correct and replace keys
		_, err = s.runCommand(fmt.Sprintf("sudo mkdir -p /home/<USER>/.ssh", username))
		if err != nil {
			return fmt.Errorf("ensuring .ssh dir for %s failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("echo -e '%s' | sudo tee /home/<USER>/.ssh/authorized_keys > /dev/null", publicSshKey, username))
		if err != nil {
			return fmt.Errorf("writing user's %s public SSH key failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chown -R %s:%s /home/<USER>/.ssh", username, username, username))
		if err != nil {
			return fmt.Errorf("chown .ssh for %s failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chmod 700 /home/<USER>/.ssh && sudo chmod 600 /home/<USER>/.ssh/authorized_keys", username, username))
		if err != nil {
			return fmt.Errorf("chmod .ssh/authorized_keys for %s failed: %s", username, err)
		}
	}
	return nil
}

// RemoveUserSshKey removes a user's SSH key(s) from the remote server but leaves their home directory otherwise intact
func (s *SSHClient) RemoveUserSshKey(username string) error {
	// Remove common authorized_keys locations; ignore if not present
	cmd := fmt.Sprintf("sudo bash -lc 'rm -f /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys2; shopt -s nullglob; files=(/home/<USER>/.ssh/authorized_keys.d/*); [ ${#files[@]} -gt 0 ] && rm -f /home/<USER>/.ssh/authorized_keys.d/* || true'", username)
	_, err := s.runCommand(cmd)
	if err != nil {
		return fmt.Errorf("removing user's ssh key(s) for %s failed: %s", username, err)
	}
	return nil
}

// VerifyUserKeyPresent checks whether the given public key is present in any authorized_keys file.
func (s *SSHClient) VerifyUserKeyPresent(username string, publicSshKey string) (bool, error) {
	cmd := fmt.Sprintf("sudo bash -lc 'shopt -s nullglob; key=%[2]q; files=(/home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys2 /home/<USER>/.ssh/authorized_keys.d/*); for f in \"${files[@]}\"; do [ -f \"$f\" ] || continue; if grep -Fq -- \"$key\" \"$f\"; then echo FOUND; exit 0; fi; done; echo MISS; exit 0'", username, publicSshKey)
	out, err := s.runCommand(cmd)
	if err != nil {
		return false, err
	}
	return strings.Contains(out, "FOUND"), nil
}

// VerifyUserAuthorizedKeysAbsent checks that no authorized_keys files exist or that all are empty.
func (s *SSHClient) VerifyUserAuthorizedKeysAbsent(username string) (bool, error) {
	cmd := fmt.Sprintf("sudo bash -lc 'shopt -s nullglob; files=(/home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys2 /home/<USER>/.ssh/authorized_keys.d/*); if [ ${#files[@]} -eq 0 ]; then echo ABSENT; exit 0; fi; for f in \"${files[@]}\"; do if [ -s \"$f\" ]; then echo PRESENT; exit 0; fi; done; echo ABSENT; exit 0'", username)
	out, err := s.runCommand(cmd)
	if err != nil {
		return false, err
	}
	return strings.Contains(out, "ABSENT"), nil
}

// runCommand runs a command to a remote server through SSH
func (s *SSHClient) runCommand(cmd string) (string, error) {
	session, err := s.client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create session: %w", err)
	}
	defer session.Close()
	var stdoutBuf bytes.Buffer
	session.Stdout = &stdoutBuf
	if err := session.Run(cmd); err != nil {
		return "", fmt.Errorf("failed to run command: %v", err)
	}

	return stdoutBuf.String(), nil
}
