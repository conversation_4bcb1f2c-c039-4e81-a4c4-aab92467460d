package db

import (
    "context"

    "github.com/jackc/pgx/v5/pgtype"
)

// InsertDeletedUsernameHistory records a deleted username for a user.
// This is called when a user's custom_username changes; the previous
// username is inserted into history with the deletion timestamp.
func (q *Queries) InsertDeletedUsernameHistory(ctx context.Context, userID pgtype.UUID, deletedUsername string) error {
    _, err := q.db.Exec(ctx,
        `INSERT INTO usernames_history (user_id, username) VALUES ($1, $2)`,
        userID, deletedUsername,
    )
    return err
}

