package users

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	"golang.org/x/crypto/ssh"
)

func SetUserSshKey(queries *db.Queries, userID string, sshKey string, sshKeyLabel string) (*User, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	if _, _, _, _, err = ssh.ParseAuthorizedKey([]byte(sshKey)); err != nil {
		return nil, fmt.Errorf("invalid public SSH key")
	}

	updatedUser, err := queries.SetUserSshKey(context.Background(), db.SetUserSshKeyParams{
		SshKey:      pgtype.Text{String: sshKey, Valid: true},
		SshKeyLabel: pgtype.Text{String: sshKeyLabel, Valid: true},
		ID:          *userIDPgType,
	})
	if err != nil {
		return nil, err
	}

	userResponse := User{
		ID:                 userID,
		FullName:           updatedUser.FullName.String,
		Username:           updatedUser.Username,
		SSHKey:             updatedUser.SshKey.String,
		SSHKeyLabel:        updatedUser.SshKeyLabel.String,
		SSHKeyCreationDate: updatedUser.SshKeyCreationDate.Time,
	}

	return &userResponse, nil
}

func RemoveUserSshKey(queries *db.Queries, userID string) error {
    userIDPgType, err := converters.StringToPgTypeUUID(userID)
    if err != nil {
        return err
    }

    // Get the current SSH key before removing it
    currentSshKey, err := queries.GetUserSshKey(context.Background(), *userIDPgType)
    if err != nil {
        return fmt.Errorf("could not get user's current SSH key: %v", err)
    }

    // Remove the SSH key from the user (existing query)
    err = queries.RemoveUserSshKey(context.Background(), *userIDPgType)
    if err != nil {
        return fmt.Errorf("could not remove user's public SSH key: %v", err)
    }

    // Record the deleted SSH key in history (new query)
    if currentSshKey.Valid && strings.TrimSpace(currentSshKey.String) != "" {
        if err := queries.InsertDeletedSshKeyHistory(
            context.Background(),
            db.InsertDeletedSshKeyHistoryParams{UserID: *userIDPgType, SshKey: currentSshKey},
        ); err != nil {
            fmt.Printf("Warning: Failed to insert deleted SSH key history for user %s: %v\n", userID, err)
        }
    }

    return nil
}

// Propagate removal of a user's SSH key to all assigned instances for the user's current custom username.
// This now uses the same robust logic as RevokeUserSshAccessForUsernameOnInstances
func PropagateUserSshKeyRemovalToInstances(queries *db.Queries, userID string, awsRootRegion string, secretKey string) error {
	fmt.Printf("SSH_KEY_REMOVAL: starting for user %s\n", userID)
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		fmt.Printf("SSH_KEY_REMOVAL: UUID conversion failed: %v\n", err)
		return err
	}

	customUsername, err := queries.GetUserCustomUsername(context.Background(), *userIDPgType)
	if err != nil {
		fmt.Printf("SSH_KEY_REMOVAL: GetUserCustomUsername failed: %v\n", err)
		return err
	}
	if !customUsername.Valid || strings.TrimSpace(customUsername.String) == "" {
		fmt.Printf("SSH_KEY_REMOVAL: no custom username for user %s, nothing to do\n", userID)
		return nil // nothing to do
	}
	fmt.Printf("SSH_KEY_REMOVAL: removing keys for username '%s' using unified revoke logic\n", customUsername.String)

	// Use the same robust logic as username changes
	return RevokeUserSshAccessForUsernameOnInstances(queries, userID, customUsername.String, awsRootRegion, secretKey)
}

// Add current user's SSH key to all assigned instances for the user's current custom username.
func PropagateUserSshKeyAdditionToInstances(queries *db.Queries, userID string, awsRootRegion string, secretKey string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return err
	}
	customUsername, err := queries.GetUserCustomUsername(context.Background(), *userIDPgType)
	if err != nil {
		return err
	}
	if !customUsername.Valid || strings.TrimSpace(customUsername.String) == "" {
		return nil // nothing to do
	}
	userSSHKey, err := queries.GetUserSshKey(context.Background(), *userIDPgType)
	if err != nil {
		return err
	}
	if !userSSHKey.Valid || strings.TrimSpace(userSSHKey.String) == "" {
		return nil
	}

	engagements, err := queries.GetUserEngagements(context.Background(), *userIDPgType)
	if err != nil {
		return err
	}
	// Prepare secrets manager and per-account cache
	sm, err := keys.NewSecretsManager(awsRootRegion)
	if err != nil {
		return fmt.Errorf("secrets manager init failed: %w", err)
	}
	type acctCred struct{ key, pass, adminUser string }
	cache := map[string]acctCred{}

	for _, engagement := range engagements {
		instances, err := queries.GetEngagementCloudInstances(context.Background(), engagement.ID)
		if err != nil || len(instances) == 0 {
			continue
		}
		for _, inst := range instances {
			if inst.PublicIpv4Address == nil {
				continue
			}
			// Only process running instances
			if !inst.CloudInstanceState.Valid || inst.CloudInstanceState.CiStateEnum != "running" {
				continue
			}
			var accountUUID pgtype.UUID
			var isAzure bool
			if inst.AwsAccountID.Valid {
				accountUUID = inst.AwsAccountID
				isAzure = false
			} else if inst.AzureTenantID.Valid {
				accountUUID = inst.AzureTenantID
				isAzure = true
			} else {
				continue
			}
			accountIDStr, _ := converters.PgTypeUUIDToString(accountUUID)
			cred, ok := cache[*accountIDStr]
			if !ok {
				secretValues, err := sm.GetSecret(*accountIDStr)
				if err != nil {
					continue
				}
				sshPrivateKey, ok2 := secretValues["ssh_private_key"]
				if !ok2 {
					continue
				}

				// Use the correct query based on provider type
				var policyID, statusID string
				if isAzure {
					azureKey, err := queries.GetAzureTenantSshPrivateKey(context.Background(), accountUUID)
					if err != nil {
						continue
					}
					policyID = azureKey.PolicyID
					statusID = azureKey.StatusID
				} else {
					awsKey, err := queries.GetAccountSshPrivateKey(context.Background(), accountUUID)
					if err != nil {
						continue
					}
					policyID = awsKey.PolicyID
					statusID = awsKey.StatusID
				}
				decryptedSecret, err := keys.DecryptSecret(policyID, statusID, secretKey)
				if err != nil {
					continue
				}
				adminUserFromSecret := ""
				if au, ok := secretValues["admin_username"]; ok {
					adminUserFromSecret = au
				}
				cred = acctCred{key: sshPrivateKey, pass: string(decryptedSecret), adminUser: adminUserFromSecret}
				cache[*accountIDStr] = cred
			}
			addr := fmt.Sprintf("%s:22", strings.Replace(inst.PublicIpv4Address.String(), "\"", "", -1))
			
			// Optimized primary user + fallback approach
			var primaryUser string
			var fallbackUsers []string

			if isAzure {
				primaryUser = "azureuser"
				fallbackUsers = []string{"admin", "ubuntu"}
			} else {
				primaryUser = "admin"
				fallbackUsers = []string{"ubuntu", "ec2-user"}
			}

			fmt.Printf("PropagateUserSshKeyAdditionToInstances: trying primary SSH user '%s' for %s\n", primaryUser, addr)
			var sshClient *keys.SSHClient

			// Try primary user with more retries (higher chance of success)
			tmp := keys.NewSSHClient(addr, primaryUser, cred.key, cred.pass, 5, 5*time.Second)
			if err := tmp.Connect(); err == nil {
				sshClient = tmp
				fmt.Printf("PropagateUserSshKeyAdditionToInstances: SSH connection successful with primary user '%s' to %s\n", primaryUser, addr)
			} else {
				fmt.Printf("PropagateUserSshKeyAdditionToInstances: primary user '%s' failed to %s: %v, trying fallbacks\n", primaryUser, addr, err)

				// Try fallback users with fewer retries
				for _, fallbackUser := range fallbackUsers {
					fmt.Printf("PropagateUserSshKeyAdditionToInstances: trying fallback SSH user '%s' to %s\n", fallbackUser, addr)
					tmp := keys.NewSSHClient(addr, fallbackUser, cred.key, cred.pass, 2, 5*time.Second)
					if err := tmp.Connect(); err == nil {
						sshClient = tmp
						fmt.Printf("PropagateUserSshKeyAdditionToInstances: SSH connection successful with fallback user '%s' to %s\n", fallbackUser, addr)
						break
					} else {
						fmt.Printf("PropagateUserSshKeyAdditionToInstances: fallback user '%s' failed to %s: %v\n", fallbackUser, addr, err)
					}
				}
			}
			if sshClient == nil {
				continue
			}
			if err := sshClient.AddUser(customUsername.String, userSSHKey.String, "fusionx"); err != nil {
				fmt.Printf("Warning: add key for %s on %s failed: %v\n", customUsername.String, addr, err)
			} else {
				if ok, verr := sshClient.VerifyUserKeyPresent(customUsername.String, userSSHKey.String); verr != nil {
					fmt.Printf("Verify add for %s on %s failed: %v\n", customUsername.String, addr, verr)
				} else if ok {
					fmt.Printf("Verify add: key present for %s on %s\n", customUsername.String, addr)
				} else {
					fmt.Printf("Verify add: key NOT found for %s on %s\n", customUsername.String, addr)
				}
			}
			sshClient.Close()
		}
	}
	return nil
}

// Revoke access for a specific username (old username) across all assigned instances of the user.
func RevokeUserSshAccessForUsernameOnInstances(queries *db.Queries, userID string, targetUsername string, awsRootRegion string, secretKey string) error {
	fmt.Printf("REVOKE_ACCESS: Starting for user=%s targetUsername='%s'\n", userID, targetUsername)

	if strings.TrimSpace(targetUsername) == "" {
		fmt.Printf("REVOKE_ACCESS: user=%s - empty targetUsername, nothing to do\n", userID)
		return nil
	}
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		fmt.Printf("REVOKE_ACCESS: user=%s - UUID conversion failed: %v\n", userID, err)
		return err
	}
	engagements, err := queries.GetUserEngagements(context.Background(), *userIDPgType)
	if err != nil {
		fmt.Printf("REVOKE_ACCESS: user=%s - GetUserEngagements failed: %v\n", userID, err)
		return err
	}

	fmt.Printf("REVOKE_ACCESS: user=%s targetUsername='%s' found %d engagements\n", userID, targetUsername, len(engagements))
	// Prepare secrets manager and per-account cache
	sm, err := keys.NewSecretsManager(awsRootRegion)
	if err != nil {
		fmt.Printf("REVOKE_ACCESS: user=%s - secrets manager init failed: %v\n", userID, err)
		return fmt.Errorf("secrets manager init failed: %w", err)
	}
	type acctCred struct{ key, pass, adminUser string }
	cache := map[string]acctCred{}

	for _, engagement := range engagements {
		engagementIDStr, _ := converters.PgTypeUUIDToString(engagement.ID)
		fmt.Printf("REVOKE_ACCESS: user=%s targetUsername='%s' processing engagement=%s\n", userID, targetUsername, *engagementIDStr)

		instances, err := queries.GetEngagementCloudInstances(context.Background(), engagement.ID)
		if err != nil {
			fmt.Printf("REVOKE_ACCESS: user=%s engagement=%s - GetEngagementCloudInstances failed: %v\n", userID, *engagementIDStr, err)
			continue
		}
		if len(instances) == 0 {
			fmt.Printf("REVOKE_ACCESS: user=%s engagement=%s - no instances\n", userID, *engagementIDStr)
			continue
		}

		fmt.Printf("REVOKE_ACCESS: user=%s engagement=%s found %d instances\n", userID, *engagementIDStr, len(instances))

		for _, inst := range instances {
			if inst.PublicIpv4Address == nil {
				nodeIDStr, _ := converters.PgTypeUUIDToString(inst.NodeID)
				fmt.Printf("REVOKE_ACCESS: user=%s engagement=%s instance node_id=%s - no public IP, skipping\n", userID, *engagementIDStr, *nodeIDStr)
				continue
			}
			nodeIDStr, _ := converters.PgTypeUUIDToString(inst.NodeID)
			instanceIP := strings.Replace(inst.PublicIpv4Address.String(), "\"", "", -1)

			// Only process running instances
			if !inst.CloudInstanceState.Valid || inst.CloudInstanceState.CiStateEnum != "running" {
				state := "unknown"
				if inst.CloudInstanceState.Valid {
					state = string(inst.CloudInstanceState.CiStateEnum)
				}
				fmt.Printf("REVOKE_ACCESS: user=%s engagement=%s instance node_id=%s ip=%s - not running (state: %s), skipping\n", userID, *engagementIDStr, *nodeIDStr, instanceIP, state)
				continue
			}

			fmt.Printf("REVOKE_ACCESS: user=%s engagement=%s instance node_id=%s ip=%s - processing\n", userID, *engagementIDStr, *nodeIDStr, instanceIP)

			var accountUUID pgtype.UUID
			var isAzure bool
			if inst.AwsAccountID.Valid {
				accountUUID = inst.AwsAccountID
				isAzure = false
			} else if inst.AzureTenantID.Valid {
				accountUUID = inst.AzureTenantID
				isAzure = true
			} else {
				fmt.Printf("REVOKE_ACCESS: user=%s engagement=%s instance node_id=%s - no account ID, skipping\n", userID, *engagementIDStr, *nodeIDStr)
				continue
			}
			accountIDStr, _ := converters.PgTypeUUIDToString(accountUUID)
			cred, ok := cache[*accountIDStr]
			if !ok {
				fmt.Printf("REVOKE_ACCESS: user=%s account=%s - fetching credentials (Azure: %v)\n", userID, *accountIDStr, isAzure)
				secretValues, err := sm.GetSecret(*accountIDStr)
				if err != nil {
					fmt.Printf("REVOKE_ACCESS: user=%s account=%s - GetSecret failed: %v\n", userID, *accountIDStr, err)
					continue
				}
				sshPrivateKey, ok2 := secretValues["ssh_private_key"]
				if !ok2 {
					fmt.Printf("REVOKE_ACCESS: user=%s account=%s - ssh_private_key missing\n", userID, *accountIDStr)
					continue
				}

				// Use the correct query based on provider type
				var policyID, statusID string
				if isAzure {
					azureKey, err := queries.GetAzureTenantSshPrivateKey(context.Background(), accountUUID)
					if err != nil {
						fmt.Printf("REVOKE_ACCESS: user=%s account=%s - GetAzureTenantSshPrivateKey failed: %v\n", userID, *accountIDStr, err)
						continue
					}
					policyID = azureKey.PolicyID
					statusID = azureKey.StatusID
				} else {
					awsKey, err := queries.GetAccountSshPrivateKey(context.Background(), accountUUID)
					if err != nil {
						fmt.Printf("REVOKE_ACCESS: user=%s account=%s - GetAccountSshPrivateKey failed: %v\n", userID, *accountIDStr, err)
						continue
					}
					policyID = awsKey.PolicyID
					statusID = awsKey.StatusID
				}
				decryptedSecret, err := keys.DecryptSecret(policyID, statusID, secretKey)
				if err != nil {
					fmt.Printf("REVOKE_ACCESS: user=%s account=%s - DecryptSecret failed: %v\n", userID, *accountIDStr, err)
					continue
				}
				adminUserFromSecret := ""
				if au, ok := secretValues["admin_username"]; ok {
					adminUserFromSecret = au
				}
				cred = acctCred{key: sshPrivateKey, pass: string(decryptedSecret), adminUser: adminUserFromSecret}
				cache[*accountIDStr] = cred
				fmt.Printf("REVOKE_ACCESS: user=%s account=%s - credentials cached\n", userID, *accountIDStr)
			}
			addr := fmt.Sprintf("%s:22", instanceIP)
			
			// Optimized primary user + fallback approach for SSH key revocation
			var primaryUser string
			var fallbackUsers []string

			if isAzure {
				primaryUser = "azureuser"
				fallbackUsers = []string{"admin", "ubuntu"}
			} else {
				primaryUser = "admin"
				fallbackUsers = []string{"ubuntu", "ec2-user"}
			}

			fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - trying primary SSH user '%s'\n", userID, *nodeIDStr, instanceIP, primaryUser)
			var sshClient *keys.SSHClient

			// Try primary user with more retries (higher chance of success)
			tmp := keys.NewSSHClient(addr, primaryUser, cred.key, cred.pass, 5, 5*time.Second)
			if err := tmp.Connect(); err == nil {
				sshClient = tmp
				fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - SSH connection SUCCESS with primary user '%s'\n", userID, *nodeIDStr, instanceIP, primaryUser)
			} else {
				fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - primary user '%s' failed: %v, trying fallbacks\n", userID, *nodeIDStr, instanceIP, primaryUser, err)

				// Try fallback users with fewer retries
				for _, fallbackUser := range fallbackUsers {
					fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - trying fallback SSH user '%s'\n", userID, *nodeIDStr, instanceIP, fallbackUser)
					tmp := keys.NewSSHClient(addr, fallbackUser, cred.key, cred.pass, 2, 5*time.Second)
					if err := tmp.Connect(); err == nil {
						sshClient = tmp
						fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - SSH connection SUCCESS with fallback user '%s'\n", userID, *nodeIDStr, instanceIP, fallbackUser)
						break
					} else {
						fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - fallback user '%s' failed: %v\n", userID, *nodeIDStr, instanceIP, fallbackUser, err)
					}
				}
			}
			if sshClient == nil {
				fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - no SSH connection possible, skipping\n", userID, *nodeIDStr, instanceIP)
				continue
			}

			fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - removing SSH keys for targetUsername='%s'\n", userID, *nodeIDStr, instanceIP, targetUsername)
			if err := sshClient.RemoveUserSshKey(targetUsername); err != nil {
				fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - FAILED to remove authorized_keys for '%s': %v\n", userID, *nodeIDStr, instanceIP, targetUsername, err)
			} else {
				fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - SUCCESS removed authorized_keys for '%s'\n", userID, *nodeIDStr, instanceIP, targetUsername)
				if ok, verr := sshClient.VerifyUserAuthorizedKeysAbsent(targetUsername); verr != nil {
					fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - verify removal for '%s' FAILED: %v\n", userID, *nodeIDStr, instanceIP, targetUsername, verr)
				} else if ok {
					fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - verify removal for '%s': NO KEYS FOUND (SUCCESS)\n", userID, *nodeIDStr, instanceIP, targetUsername)
				} else {
					fmt.Printf("REVOKE_ACCESS: user=%s instance node_id=%s ip=%s - verify removal for '%s': KEYS STILL PRESENT (PROBLEM!)\n", userID, *nodeIDStr, instanceIP, targetUsername)
				}
			}
			sshClient.Close()
		}
	}

	fmt.Printf("REVOKE_ACCESS: COMPLETED for user=%s targetUsername='%s'\n", userID, targetUsername)
	return nil
}
