package users

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/hashicorp/go-set/v3"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	"golang.org/x/crypto/ssh"
)

// performKeyManagementToInstance adds and removes given users from a Cloud Instance using SSH
func performKeyManagementToInstance(queries *db.Queries, cloudInstance db.GetEngagementCloudInstancesRow, engagementPrivateSshKey string, passphrase string, usersToBeAdded []db.User, usersToBeRemoved []db.User) {
	// Determine the correct admin user based on provider
	adminUser := "admin" // Default for AWS
	if cloudInstance.Provider == "AZURE" {
		adminUser = "azureuser"
	}

	// Connect to instance using SSH
	sshClient := keys.NewSSHClient(fmt.Sprintf("%s:22", strings.Replace(cloudInstance.PublicIpv4Address.String(), "\"", "", -1)), adminUser, engagementPrivateSshKey, passphrase, 10, 2*time.Second)
	err := sshClient.Connect()
	if err != nil {
		_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
			Message: fmt.Sprintf("Failed to initialise SSH connection to instance: %v", err.Error()),
			Type:    db.LogsAssignmentsTypeEnumSSHCONNECTION,
			Status:  db.LogsAssignmentsStatusEnumERROR,
			NodeID:  cloudInstance.NodeID,
		})
	}

	// Add users
	for _, userToBeAdded := range usersToBeAdded {
		userCanBeAdded := true
		_, _, _, _, invalidSshKeyErr := ssh.ParseAuthorizedKey([]byte(userToBeAdded.SshKey.String))
		if invalidSshKeyErr != nil {
			userCanBeAdded = false
			_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
				Message:                fmt.Sprintf("Invalid user public SSH key, user %s will not be created to the instance with IP %s", userToBeAdded.Username, cloudInstance.PublicIpv4Address),
				Type:                   db.LogsAssignmentsTypeEnumUSERASSIGNMENT,
				Status:                 db.LogsAssignmentsStatusEnumERROR,
				UserID:                 userToBeAdded.ID,
				UserCustomUsernameUsed: userToBeAdded.CustomUsername,
				NodeID:                 cloudInstance.NodeID,
			})
		}
		if !userToBeAdded.CustomUsername.Valid || len(userToBeAdded.CustomUsername.String) == 0 {
			userCanBeAdded = false
			_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
				Message:                fmt.Sprintf("Invalid custom username for user, user '%s' will not be created to the instance with IP %s", userToBeAdded.Username, cloudInstance.PublicIpv4Address),
				Type:                   db.LogsAssignmentsTypeEnumUSERASSIGNMENT,
				Status:                 db.LogsAssignmentsStatusEnumERROR,
				UserID:                 userToBeAdded.ID,
				UserCustomUsernameUsed: userToBeAdded.CustomUsername,
				NodeID:                 cloudInstance.NodeID,
			})
		}
		if userCanBeAdded {
			err = sshClient.AddUser(userToBeAdded.CustomUsername.String, userToBeAdded.SshKey.String, "fusionx")
			if err != nil {
				_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
					Message:                fmt.Sprintf("Failed to add user to instance: %v", err.Error()),
					Type:                   db.LogsAssignmentsTypeEnumUSERASSIGNMENT,
					Status:                 db.LogsAssignmentsStatusEnumERROR,
					UserID:                 userToBeAdded.ID,
					UserCustomUsernameUsed: userToBeAdded.CustomUsername,
					NodeID:                 cloudInstance.NodeID,
				})
			} else {
				_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
					Message:                "User added to instance",
					Type:                   db.LogsAssignmentsTypeEnumUSERASSIGNMENT,
					Status:                 db.LogsAssignmentsStatusEnumSUCCESS,
					UserID:                 userToBeAdded.ID,
					UserCustomUsernameUsed: userToBeAdded.CustomUsername,
					NodeID:                 cloudInstance.NodeID,
				})
			}
		}
	}
	// Remove users' SSH keys
	for _, userToBeRemoved := range usersToBeRemoved {
		err = sshClient.RemoveUserSshKey(userToBeRemoved.CustomUsername.String)
		if err != nil {
			_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
				Message:                fmt.Sprintf("Failed to remove user from instance: %v", err.Error()),
				Type:                   db.LogsAssignmentsTypeEnumUSERREMOVAL,
				Status:                 db.LogsAssignmentsStatusEnumERROR,
				UserID:                 userToBeRemoved.ID,
				UserCustomUsernameUsed: userToBeRemoved.CustomUsername,
				NodeID:                 cloudInstance.NodeID,
			})
		} else {
			_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
				Message:                "User removed from instance",
				Type:                   db.LogsAssignmentsTypeEnumUSERREMOVAL,
				Status:                 db.LogsAssignmentsStatusEnumSUCCESS,
				UserID:                 userToBeRemoved.ID,
				UserCustomUsernameUsed: userToBeRemoved.CustomUsername,
				NodeID:                 cloudInstance.NodeID,
			})
		}
	}
	err = sshClient.Close()
	if err != nil {
		_ = queries.InsertAssignmentLog(context.Background(), db.InsertAssignmentLogParams{
			Message: fmt.Sprintf("Failed to close SSH connection to instance: %v", err.Error()),
			Type:    db.LogsAssignmentsTypeEnumSSHCONNECTION,
			Status:  db.LogsAssignmentsStatusEnumERROR,
			NodeID:  cloudInstance.NodeID,
		})
	}
}

// AssignUsersToEngagement adds and removes users from an Engagement and its associated Cloud Instances using SSH
func AssignUsersToEngagement(awsRootRegion string, secretKey string, logger *slog.Logger, queries *db.Queries, engagementID string, assignedUserIDs []string) error {
	engagementIDPgType, err := converters.StringToPgTypeUUID(engagementID)
	if err != nil {
		return err
	}

	if len(assignedUserIDs) == 0 {
		err = queries.DeleteUserFromEngagement(context.Background(), *engagementIDPgType)

		if err != nil {
			return fmt.Errorf("error deleting all users from engagement: %v", err)
		}
		return nil
	} else {

		// Get all the IDs of the Engagement Users
		existingEngagementUsers, _ := queries.GetEngagementUsers(context.Background(), *engagementIDPgType)
		existingEngagementUserIDs := make([]pgtype.UUID, len(existingEngagementUsers))
		for _, existingEngagementUser := range existingEngagementUsers {
			existingEngagementUserIDs = append(existingEngagementUserIDs, existingEngagementUser.UserID)
		}

		existing := set.From[pgtype.UUID](existingEngagementUserIDs)

		var assignedUserIDsPgType []pgtype.UUID
		for _, assignedUserID := range assignedUserIDs {
			assignedUserIDPgType, err := converters.StringToPgTypeUUID(assignedUserID)
			if err != nil || assignedUserIDPgType == nil {
				return fmt.Errorf("failed to convert assignedUserID %s to pgtype.UUID: %v", assignedUserID, err)
			}
			assignedUserIDsPgType = append(assignedUserIDsPgType, *assignedUserIDPgType)
		}

		// Create a set from the users that are requested to be assigned
		assigned := set.From[pgtype.UUID](assignedUserIDsPgType)

		// Find out the new users that need to be added to the Engagement
		toBeAdded := assigned.Difference(existing)

		// Find out the existing users that need to be removed from the Engagement
		toBeRemoved := existing.Difference(assigned)

		// Find out the existing users that will have no action performed on them
		noAction := existing.Intersect(assigned)

		usersToBeAdded, err := queries.GetUsersFromIDs(context.Background(), toBeAdded.Slice())
		if err != nil {
			return fmt.Errorf("failed to get users from IDs: %v", err)
		}
		usersToBeRemoved, err := queries.GetUsersFromIDs(context.Background(), toBeRemoved.Slice())
		if err != nil {
			return fmt.Errorf("failed to get users from IDs: %v", err)
		}
		usersNoAction, err := queries.GetUsersFromIDs(context.Background(), noAction.Slice())
		if err != nil {
			return fmt.Errorf("failed to get users from IDs: %v", err)
		}

		logger.Info("Users to be added to Engagement", "engagement_id", engagementID, "users", usersToBeAdded)
		logger.Info("Users to be removed from Engagement", "engagement_id", engagementID, "users", usersToBeRemoved)
		logger.Info("Users with no action for Engagement", "engagement_id", engagementID, "users", usersNoAction)

		err = queries.DeleteUsersFromEngagement(context.Background(), db.DeleteUsersFromEngagementParams{
			EngagementID: *engagementIDPgType,
			Column2:      toBeRemoved.Slice(),
		})
		if err != nil {
			return err
		}

		err = queries.AddUsersToEngagement(context.Background(), db.AddUsersToEngagementParams{
			EngagementID: *engagementIDPgType,
			Column2:      assignedUserIDsPgType,
		})
		if err != nil {
			return err
		}

		// Get all Cloud Instances of the Engagement
		cloudInstances, err := queries.GetEngagementCloudInstances(context.Background(), *engagementIDPgType)
		if err != nil {
			return err
		}

		// Get the private SSH key of the Engagement
		sm, err := keys.NewSecretsManager(awsRootRegion)
		if err != nil {
			logger.Error("Error initializing SecretsManager for Engagement",
				"engagement_id", engagementID,
				"error", err.Error())
			return err
		}

		for _, cloudInstance := range cloudInstances {
			var accountIDString string
			var accountUUID pgtype.UUID

			if cloudInstance.AwsAccountID.Valid {
				accountIDString = cloudInstance.AwsAccountID.String()
				accountUUID = cloudInstance.AwsAccountID
				// PLACEHOLDER AZURE - UNCOMMENT THE BELOW
				} else if cloudInstance.AzureTenantID.Valid {
					accountIDString = cloudInstance.AzureTenantID.String()
					accountUUID = cloudInstance.AzureTenantID
			} else {
				logger.Warn("Cloud instance has no associated AWS or Azure account", "node_id", cloudInstance.NodeID)
				continue
			}

			secretValues, err := sm.GetSecret(accountIDString)
			if err != nil {
				logger.Error("Failed to get secret for account", "account_id", accountIDString, "error", err.Error())
				return err
			}
			sshPrivateKey, found := secretValues["ssh_private_key"]
			if !found {
				logger.Error("Failed to get secret for Engagement",
					"engagement_id", engagementID)
				return fmt.Errorf("failed to get SSH private key for Engagement")
			}

			accountPrivateSshKey, err := queries.GetAccountSshPrivateKey(context.Background(), accountUUID)
			if err != nil {
				return err
			}

			decryptedSecret, err := keys.DecryptSecret(accountPrivateSshKey.PolicyID, accountPrivateSshKey.StatusID, secretKey)
			if err != nil {
				return err
			}

			go performKeyManagementToInstance(
				queries,
				cloudInstance,
				sshPrivateKey,
				string(decryptedSecret),
				usersToBeAdded,
				usersToBeRemoved,
			)
		}
	}
	return nil
}

func SyncAdminUserEngagements(queries db.Queries, ctx context.Context, userID string) error {
	fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: Starting for user=%s\n", userID)

	userIdPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s UUID conversion failed: %v\n", userID, err)
		return err
	}

	// GetValidAdminUsers might return (nil, nil) if the user is not an admin.
	adminUser, err := queries.GetValidAdminUsers(ctx, *userIdPgType)
	if err != nil {
		fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s GetValidAdminUsers failed: %v\n", userID, err)
		return err
	}

	if adminUser != nil {
		fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s is admin, syncing to all engagements\n", userID)
		engagements, err := queries.GetAllEngagements(ctx)
		if err != nil {
			fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s GetAllEngagements failed: %v\n", userID, err)
			return err
		}

		fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s found %d engagements to sync\n", userID, len(engagements))

		for _, engagement := range engagements {
			engagementIDStr, _ := converters.PgTypeUUIDToString(engagement.ID)
			err = queries.AddAdminUserToEngagement(ctx, db.AddAdminUserToEngagementParams{
				UserID:       *userIdPgType,
				EngagementID: engagement.ID,
			})
			if err != nil {
				fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s engagement=%s AddAdminUserToEngagement failed: %v\n", userID, *engagementIDStr, err)
				return err
			} else {
				fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s engagement=%s added successfully\n", userID, *engagementIDStr)
			}
		}
		fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s completed admin sync to %d engagements\n", userID, len(engagements))
	} else {
		fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: user=%s is not admin, no sync needed\n", userID)
	}

	fmt.Printf("SYNC_ADMIN_ENGAGEMENTS: COMPLETED for user=%s\n", userID)
	return nil
}
