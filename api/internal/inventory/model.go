package inventory

import (
	"context"
	"fmt"
	"net/netip"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

type NodeType struct {
	NodeType string `json:"node_type"`
	Count    int    `json:"count"`
}

type NodeEngagementGroup struct {
	NodeID      string    `json:"id" format:"uuid"`
	NodeType    string    `json:"type"`
	NodeName    string    `json:"name"`
	UpdatedAt   time.Time `json:"updated_at"`
	Title       string    `json:"engagement_name"`
	IsActive    bool      `json:"is_active"`
	NodeGroupID string    `json:"node_group_id"`
	ClientName  string    `json:"client_name"`
}

type Node struct {
	ID        string          `json:"id" format:"uuid"`
	Type      db.NodeTypeEnum `json:"type"`
	Name      string          `json:"name"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

type InventoryCloudInstance struct {
	Node
	OperatingSystemImageID string          `json:"operating_system_image_id"`
	Provider               db.ProviderEnum `json:"provider"`
	Name                   string          `json:"name"`
	Message                string          `json:"log_message"`
	Region                 string          `json:"region"`
	PublicIpV4Address      *netip.Addr     `json:"public_ipv4_address" format:"ipv4"`
	OpenPorts              []int32         `json:"open_ports"`
	Title                  string          `json:"title"`
	ClientName             string          `json:"client_name"`
	CideploymentStatus     string          `json:"ci_deployment_status" enum:"PENDING,IN-PROGRESS,SUCCESS,WARNING,ERROR"`
	CloudInstanceState     string          `json:"cloud_instance_state" enum:"pending,running,stopping,stopped,shutting-down,terminated,error,new"`
	CloudInstanceID        string          `json:"cloud_instance_id" example:"i-034295fe21c3bebf7"`
	CreatedAt              time.Time       `json:"node_created_at"`

	EventIp string `json:"event_ip" format:"ipv4"`

	// Additional fields needed for compatibility with NodeEngagementGroup
	EngagementName string `json:"engagement_name"`
	IsActive       bool   `json:"is_active"`
	NodeGroupID    string `json:"node_group_id"`
}

type CloudInstanceNodeGroups struct {
	ID             string                   `json:"id" format:"uuid"`
	Name           string                   `json:"name"`
	IsActive       bool                     `json:"is_active"`
	CreatedAt      time.Time                `json:"created_at"`
	UpdatedAt      time.Time                `json:"updated_at"`
	CloudInstances []InventoryCloudInstance `json:"cloud_instances"`
}

func GetNodeTypes(queries *db.Queries, userID pgtype.UUID) ([]NodeType, *int64, error) {
	nodeTypesDB, err := queries.GetNodeTypes(context.TODO(), userID)
	if err != nil {
		return nil, nil, err
	}

	nodeTypes := make([]NodeType, 0)

	var totalNodes int64 = 0
	for _, nodeTypeDB := range nodeTypesDB {
		nodeTypes = append(nodeTypes, NodeType{
			NodeType: string(nodeTypeDB.NodeType),
			Count:    int(nodeTypeDB.Count),
		})
		totalNodes += nodeTypeDB.Count
	}
	return nodeTypes, &totalNodes, nil
}

func GetEngagementNodesGroups(queries *db.Queries, userID string) ([]NodeEngagementGroup, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}
	nodeGroups := make([]NodeEngagementGroup, 0)
	graphNodes, err := queries.GetEngagementNodesGroups(context.Background(), *userIDPgType)
	if err != nil {
		return nil, err
	}

	for _, node := range graphNodes {
		nodeIDString, _ := converters.PgTypeUUIDToString(node.NodeID)
		nodeGroups = append(nodeGroups, NodeEngagementGroup{
			NodeType:   string(node.NodeType),
			NodeName:   node.NodeName,
			Title:      node.EngagementName,
			IsActive:   node.IsActive,
			ClientName: node.ClientName,
			NodeID:     *nodeIDString,
			UpdatedAt:  node.NodeUpdatedAt.Time,
		})
	}
	return nodeGroups, nil
}

func GetInventoryCloudInstances(queries *db.Queries, engagementIDs []pgtype.UUID, userID pgtype.UUID) ([]InventoryCloudInstance, error) {
	cloudInstancesDB, err := queries.GetInventoryCloudInstancesForUser(context.Background(), db.GetInventoryCloudInstancesForUserParams{
		ID:  userID,
		Ids: engagementIDs,
	})
	if err != nil {
		return nil, err
	}

	cloudInstances := make([]InventoryCloudInstance, 0)

	for _, cloudInstanceDB := range cloudInstancesDB {
		nodeIDString, _ := converters.PgTypeUUIDToString(cloudInstanceDB.NodeID)
		var nodeCreatedAt time.Time
		if cloudInstanceDB.NodeCreatedAt.Valid {
			nodeCreatedAt = cloudInstanceDB.NodeCreatedAt.Time
		}

		node := Node{
			ID:        *nodeIDString,
			Type:      "CLOUD_INSTANCE",
			Name:      cloudInstanceDB.Name,
			CreatedAt: nodeCreatedAt,
		}
		// Convert EventIp from the logs_nodes table

		// Convert EventIp from the query result (combines logs_nodes and current IP)
		var eventIpStr string
		if cloudInstanceDB.EventIp != nil {
			if ipStr, ok := cloudInstanceDB.EventIp.(string); ok && ipStr != "" {
				// Handle CIDR notation (e.g., "**************/32") by extracting just the IP
				if strings.Contains(ipStr, "/") {
					if addr, err := netip.ParsePrefix(ipStr); err == nil {
						eventIpStr = addr.Addr().String()
					}
				} else {
					// Handle regular IP address
					if addr, err := netip.ParseAddr(ipStr); err == nil {
						eventIpStr = addr.String()
					}
				}
			}
		}

		nodeGroupIDString, _ := converters.PgTypeUUIDToString(cloudInstanceDB.NodeGroupID)

		cloudInstance := InventoryCloudInstance{
			Node:                   node,
			OperatingSystemImageID: cloudInstanceDB.OperatingSystemImageID,
			Message:                cloudInstanceDB.LogMessage,
			Provider:               cloudInstanceDB.Provider,
			Name:                   cloudInstanceDB.Name,
			Region:                 cloudInstanceDB.Region,
			PublicIpV4Address:      cloudInstanceDB.PublicIpv4Address,
			OpenPorts:              cloudInstanceDB.OpenPorts,
			Title:                  cloudInstanceDB.Title,
			ClientName:             cloudInstanceDB.ClientName,
			CideploymentStatus:     string(cloudInstanceDB.CiDeploymentStatus),
			CloudInstanceState:     string(cloudInstanceDB.CloudInstanceState.CiStateEnum),
			CloudInstanceID:        cloudInstanceDB.CloudInstanceID.String,
			CreatedAt:              cloudInstanceDB.NodeCreatedAt.Time,

			EventIp: eventIpStr,

			// Populate the additional fields for compatibility with NodeEngagementGroup
			EngagementName: cloudInstanceDB.Title,
			IsActive:       cloudInstanceDB.NodeGroupIsActive,
			NodeGroupID:    *nodeGroupIDString,
		}
		cloudInstances = append(cloudInstances, cloudInstance)
	}

	return cloudInstances, nil
}

type CloudHost struct {
	Node
	IpAddresses      []netip.Addr `json:"ip_addresses"`
	Name             string       `json:"name"`
	AlternativeNames []string     `json:"alternative_names"`
	Title            string       `json:"title"`
	ClientName       string       `json:"client_name"`
}

func GetInventoryHosts(queries *db.Queries, engagementIDs []pgtype.UUID, userID pgtype.UUID) ([]CloudHost, error) {
	cloudHostsDB, err := queries.GetEngagementHosts(context.Background(), db.GetEngagementHostsParams{
		ID:  userID,
		Ids: engagementIDs,
	})
	if err != nil {
		return nil, err
	}

	hosts := make([]CloudHost, 0)

	for _, cloudHostDB := range cloudHostsDB {
		nodeIDString, _ := converters.PgTypeUUIDToString(cloudHostDB.NodeID)
		node := Node{
			ID:        *nodeIDString,
			Type:      "HOST",
			CreatedAt: cloudHostDB.NodeCreatedAt.Time,
			UpdatedAt: cloudHostDB.NodeUpdatedAt.Time,
		}
		host := CloudHost{
			Node:             node,
			Name:             cloudHostDB.Name,
			IpAddresses:      cloudHostDB.IpAddresses,
			AlternativeNames: cloudHostDB.AlternativeNames,
			Title:            cloudHostDB.Title,
			ClientName:       cloudHostDB.ClientName,
		}
		hosts = append(hosts, host)
	}

	return hosts, nil
}

type EmailAddress struct {
	Node
	EmailAddress string `json:"email_address"`
	Title        string `json:"title"`
	ClientName   string `json:"client_name"`
}

func GetInventoryEmailAddresses(queries *db.Queries, engagementIDs []pgtype.UUID, userID pgtype.UUID) ([]EmailAddress, error) {
	emailAddressesDB, err := queries.GetEngagementEmailAddresses(context.Background(), db.GetEngagementEmailAddressesParams{
		ID:  userID,
		Ids: engagementIDs,
	})
	if err != nil {
		return nil, err
	}

	emailAddresses := make([]EmailAddress, 0)

	for _, emailAddressDB := range emailAddressesDB {
		nodeIDString, _ := converters.PgTypeUUIDToString(emailAddressDB.NodeID)
		node := Node{
			ID:        *nodeIDString,
			Type:      "EMAIL_ADDRESS",
			Name:      emailAddressDB.EmailAddress,
			CreatedAt: emailAddressDB.NodeCreatedAt.Time,
			UpdatedAt: emailAddressDB.NodeUpdatedAt.Time,
		}
		emailAddress := EmailAddress{
			Node:         node,
			EmailAddress: emailAddressDB.EmailAddress,
			Title:        emailAddressDB.Title,
			ClientName:   emailAddressDB.ClientName,
		}
		emailAddresses = append(emailAddresses, emailAddress)
	}

	return emailAddresses, nil
}

type Person struct {
	Node
	FirstName  string `json:"first_name"`
	LastName   string `json:"last_name"`
	Email      string `json:"email"`
	Company    string `json:"company"`
	Title      string `json:"title"`
	ClientName string `json:"client_name"`
}

func GetInventoryPersons(queries *db.Queries, engagementIDs []pgtype.UUID, userID pgtype.UUID) ([]Person, error) {
	personsDB, err := queries.GetEngagementPersons(context.Background(), db.GetEngagementPersonsParams{
		ID:  userID,
		Ids: engagementIDs,
	})
	if err != nil {
		return nil, err
	}

	persons := make([]Person, 0)

	for _, personDB := range personsDB {
		nodeIDString, _ := converters.PgTypeUUIDToString(personDB.NodeID)
		node := Node{
			ID:        *nodeIDString,
			Type:      "PERSON",
			Name:      fmt.Sprintf("%s %s", personDB.FirstName, personDB.LastName.String),
			CreatedAt: personDB.NodeCreatedAt.Time,
			UpdatedAt: personDB.NodeUpdatedAt.Time,
		}
		person := Person{
			Node:       node,
			FirstName:  personDB.FirstName,
			LastName:   personDB.LastName.String,
			Email:      personDB.Email.String,
			Company:    personDB.Company.String,
			Title:      personDB.Title,
			ClientName: personDB.ClientName,
		}
		persons = append(persons, person)
	}

	return persons, nil
}

type Url struct {
	Node
	Url        string `json:"url"`
	Title      string `json:"title"`
	ClientName string `json:"client_name"`
}

func GetInventoryUrls(queries *db.Queries, engagementIDs []pgtype.UUID, userID pgtype.UUID) ([]Url, error) {
	urlsDB, err := queries.GetEngagementUrls(context.Background(), db.GetEngagementUrlsParams{
		ID:  userID,
		Ids: engagementIDs,
	})
	if err != nil {
		return nil, err
	}

	urls := make([]Url, 0)

	for _, urlDB := range urlsDB {
		nodeIDString, _ := converters.PgTypeUUIDToString(urlDB.NodeID)
		node := Node{
			ID:        *nodeIDString,
			Type:      "URL",
			Name:      urlDB.Url,
			CreatedAt: urlDB.NodeCreatedAt.Time,
			UpdatedAt: urlDB.NodeUpdatedAt.Time,
		}
		url := Url{
			Node:       node,
			Url:        urlDB.Url,
			ClientName: urlDB.ClientName,
		}
		urls = append(urls, url)
	}

	return urls, nil
}
