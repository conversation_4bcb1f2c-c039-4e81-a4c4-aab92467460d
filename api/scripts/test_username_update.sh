#!/bin/bash

# Test script for username update functionality
# This script helps verify that the username update feature works correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8000}"
USER_ID="${USER_ID:-}"
AUTH_TOKEN="${AUTH_TOKEN:-}"
OLD_USERNAME="${OLD_USERNAME:-}"
NEW_USERNAME="${NEW_USERNAME:-testuser$(date +%s)}"
INSTANCE_IP="${INSTANCE_IP:-}"
SSH_KEY_PATH="${SSH_KEY_PATH:-}"

print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Test the username update functionality"
    echo ""
    echo "Required environment variables:"
    echo "  USER_ID      - UUID of the user to test"
    echo "  AUTH_TOKEN   - Valid authentication token"
    echo ""
    echo "Optional environment variables:"
    echo "  API_BASE_URL - API base URL (default: http://localhost:8000)"
    echo "  OLD_USERNAME - Current username (will be fetched if not provided)"
    echo "  NEW_USERNAME - New username (default: testuser<timestamp>)"
    echo "  INSTANCE_IP  - IP of instance to test SSH (optional)"
    echo "  SSH_KEY_PATH - Path to SSH private key for instance testing (optional)"
    echo ""
    echo "Example:"
    echo "  USER_ID=123e4567-e89b-12d3-a456-************ \\"
    echo "  AUTH_TOKEN=your_token \\"
    echo "  ./test_username_update.sh"
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    if [[ -z "$USER_ID" ]]; then
        log_error "USER_ID environment variable is required"
        print_usage
        exit 1
    fi

    if [[ -z "$AUTH_TOKEN" ]]; then
        log_error "AUTH_TOKEN environment variable is required"
        print_usage
        exit 1
    fi

    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi

    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        log_warn "jq is not installed - JSON parsing will be limited"
    fi
}

get_current_username() {
    log_info "Fetching current user details..."

    response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        "$API_BASE_URL/users/$USER_ID")

    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    if [[ "$http_code" != "200" ]]; then
        log_error "Failed to fetch user details (HTTP $http_code)"
        echo "$body"
        exit 1
    fi

    if command -v jq &> /dev/null; then
        OLD_USERNAME=$(echo "$body" | jq -r '.user.custom_username // empty')
        if [[ -z "$OLD_USERNAME" ]]; then
            log_warn "User has no current custom username"
            OLD_USERNAME=""
        else
            log_info "Current username: $OLD_USERNAME"
        fi
    else
        log_warn "Cannot parse current username without jq"
    fi
}

update_username() {
    log_info "Updating username from '$OLD_USERNAME' to '$NEW_USERNAME'..."

    response=$(curl -s -w "\n%{http_code}" \
        -X PUT \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{\"custom_username\": \"$NEW_USERNAME\"}" \
        "$API_BASE_URL/users/$USER_ID")

    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    if [[ "$http_code" == "200" ]]; then
        log_info "Username update successful!"
        return 0
    else
        log_error "Username update failed (HTTP $http_code)"
        echo "$body"
        return 1
    fi
}

verify_database_update() {
    log_info "Verifying username update in database..."

    response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        "$API_BASE_URL/users/$USER_ID")

    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    if [[ "$http_code" != "200" ]]; then
        log_error "Failed to verify user details (HTTP $http_code)"
        return 1
    fi

    if command -v jq &> /dev/null; then
        current_username=$(echo "$body" | jq -r '.user.custom_username // empty')
        if [[ "$current_username" == "$NEW_USERNAME" ]]; then
            log_info "✅ Database update verified - username is now '$NEW_USERNAME'"
            return 0
        else
            log_error "❌ Database update failed - username is still '$current_username'"
            return 1
        fi
    else
        log_warn "Cannot verify database update without jq"
        return 0
    fi
}

test_ssh_access() {
    if [[ -z "$INSTANCE_IP" ]] || [[ -z "$SSH_KEY_PATH" ]]; then
        log_warn "Skipping SSH test - INSTANCE_IP or SSH_KEY_PATH not provided"
        return 0
    fi

    if [[ ! -f "$SSH_KEY_PATH" ]]; then
        log_error "SSH key file not found: $SSH_KEY_PATH"
        return 1
    fi

    log_info "Testing SSH access to $INSTANCE_IP with new username '$NEW_USERNAME'..."

    # Test SSH connection with new username
    if ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -o StrictHostKeyChecking=no \
           "$NEW_USERNAME@$INSTANCE_IP" "echo 'SSH test successful'" 2>/dev/null; then
        log_info "✅ SSH access with new username successful"

        # Check if home directory exists
        if ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -o StrictHostKeyChecking=no \
               "$NEW_USERNAME@$INSTANCE_IP" "test -d /home/<USER>" 2>/dev/null; then
            log_info "✅ Home directory /home/<USER>"
        else
            log_warn "❌ Home directory /home/<USER>"
        fi

        # Check if old directory still exists (if there was an old username)
        if [[ -n "$OLD_USERNAME" ]]; then
            if ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -o StrictHostKeyChecking=no \
                   "admin@$INSTANCE_IP" "test -d /home/<USER>" 2>/dev/null; then
                log_info "✅ Old directory /home/<USER>"
            else
                log_warn "Old directory /home/<USER>"
            fi
        fi

            # Verify old username cannot log in
            if [[ -n "$OLD_USERNAME" ]]; then
                log_info "Verifying old username '$OLD_USERNAME' cannot log in..."
                if ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -o StrictHostKeyChecking=no \
                       "$OLD_USERNAME@$INSTANCE_IP" "echo 'should not succeed'" 2>/dev/null; then
                    log_error "❌ Old username '$OLD_USERNAME' still has SSH access"
                    return 1
                else
                    log_info "✅ Old username '$OLD_USERNAME' cannot log in (as expected)"
                fi
            fi


        return 0
    else
        log_error "❌ SSH access with new username failed"
        return 1
    fi
}

main() {
    echo "=== Username Update Test ==="
    echo "User ID: $USER_ID"
    echo "New Username: $NEW_USERNAME"
    echo "API Base URL: $API_BASE_URL"
    echo ""

    check_prerequisites

    # Get current username if not provided
    if [[ -z "$OLD_USERNAME" ]]; then
        get_current_username
    fi

    # Perform the username update
    if ! update_username; then
        exit 1
    fi

    # Wait a moment for processing
    sleep 2

    # Verify the update
    if ! verify_database_update; then
        exit 1
    fi

    # Test SSH access if configured
    test_ssh_access

    log_info "=== Test completed ==="
    log_info "Next steps:"
    log_info "1. Check application logs for directory creation messages"
    log_info "2. If user is admin, verify they're added to all engagements"
    log_info "3. Test SSH access to all assigned instances"
}

# Handle help flag
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    print_usage
    exit 0
fi

main "$@"
