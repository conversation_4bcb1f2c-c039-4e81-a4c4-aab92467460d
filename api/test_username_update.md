# Testing Username Update Feature

## Overview
This document explains how to test the new username update feature that:
1. Updates the username in the database
2. Recreates user directories on all cloud instances where the user is assigned
3. Automatically adds admin users to all engagements

## Prerequisites for Testing

### 1. User Setup
- User must have a valid SSH key set in the system
- User must be assigned to at least one engagement with cloud instances
- For admin testing: User must have `app_role = 'Admin'` in the database

### 2. Environment Setup
- API server running with access to:
  - Database connection
  - AWS Secrets Manager (for SSH keys)
  - Cloud instances with SSH access enabled

## Test Scenarios

### Scenario 1: Regular User Username Update

**Setup:**
1. Create/use a user with:
   - Valid SSH key
   - Current custom username (e.g., "olduser")
   - Assigned to an engagement with running cloud instances

**Test Steps:**
1. Log in as the user
2. Navigate to User Settings
3. Change username from "olduser" to "newuser"
4. Submit the change

**Expected Results:**
- ✅ Username updated in database
- ✅ User can SSH to instances with new username: `ssh newuser@INSTANCE_IP`
- ✅ Old username directory preserved: `/home/<USER>
- ✅ New username directory created: `/home/<USER>
- ✅ Success message displayed in UI

### Scenario 2: Admin User Username Update

**Setup:**
1. Create/use a user with:
   - `app_role = 'Admin'` in database
   - Valid SSH key
   - Current custom username

**Test Steps:**
1. Log in as the admin user
2. Change username
3. Check engagement assignments

**Expected Results:**
- ✅ All results from Scenario 1
- ✅ User automatically added to ALL engagements in the system
- ✅ Can access instances in all engagements with new username

### Scenario 3: Error Handling

**Test Cases:**
1. **Duplicate Username:**
   - Try to change to an existing username
   - Expected: Error message "username 'X' is already taken"

2. **User Without SSH Key:**
   - User has no SSH key set
   - Expected: Username updates in DB, but no instance directory creation

3. **Network/SSH Issues:**
   - Instance unreachable or SSH fails
   - Expected: Username updates in DB, warning logged, process continues

## Manual Verification Commands

### Check Database Changes
```sql
-- Check username update
SELECT id, username, custom_username, app_role FROM users WHERE id = 'USER_ID';

-- Check admin engagement assignments
SELECT e.title, eu.user_id 
FROM engagements e 
JOIN engagements_users eu ON e.id = eu.engagement_id 
WHERE eu.user_id = 'ADMIN_USER_ID';
```

### Check Instance Directories
```bash
# SSH to instance as admin
ssh -i /path/to/engagement.pem admin@INSTANCE_IP

# Check user directories
ls -la /home/

# Check specific user directory and SSH keys
ls -la /home/<USER>/.ssh/
cat /home/<USER>/.ssh/authorized_keys

# Check sudoers entry
sudo cat /etc/sudoers.d/fusionx | grep newusername
```

### Check Application Logs
```bash
# Look for username update logs
grep -i "username" /path/to/api/logs

# Look for SSH connection logs
grep -i "ssh" /path/to/api/logs

# Look for warning messages about failed directory creation
grep -i "warning.*recreate.*directory" /path/to/api/logs
```

## API Testing with curl

```bash
# Update username (requires valid auth token)
curl -X PUT "http://localhost:8000/users/USER_ID" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"custom_username": "newusername"}'
```

## Troubleshooting

### Common Issues

1. **SSH Connection Fails:**
   - Check security groups allow SSH (port 22)
   - Verify instance is running and has public IP
   - Check engagement SSH key is accessible in Secrets Manager

2. **User Not Added to Instances:**
   - Verify user has valid SSH key in database
   - Check if user has custom_username set
   - Look for warning messages in logs

3. **Admin Not Added to All Engagements:**
   - Verify user has `app_role = 'Admin'`
   - Check `SyncAdminUserEngagements` function execution
   - Verify database constraints allow the insertions

### Debug Information

The implementation includes detailed logging:
- Info messages for successful directory creation
- Warning messages for failed operations (non-blocking)
- Error messages for critical failures

Check application logs for messages like:
- "Successfully recreated user directory for 'X' on instance Y"
- "Warning: Failed to recreate user directories for engagement X"
- "Info: Old username 'X' directory preserved on instance Y"
